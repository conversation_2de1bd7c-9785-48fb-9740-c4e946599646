<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.tqhit.battery.one">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions" />

    <permission
        android:name="com.tqhit.battery.one.permission.FINISH_OVERLAY"
        android:protectionLevel="signature" />

    <application
        android:name="com.tqhit.battery.one.BatteryApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:screenOrientation="portrait"
        android:supportsRtl="true"
        android:theme="@style/Theme.BatteryOne"
        tools:targetApi="31">
        <!-- Sample AdMob app ID: ca-app-pub-3940256099942544~3347511713 -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-9844172086883515~3386117176" />

        <activity
            android:name="com.tqhit.battery.one.activity.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.BatteryOne.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.tqhit.battery.one.activity.starting.StartingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.BatteryOne"/>

        <activity
            android:name="com.tqhit.battery.one.activity.main.MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity
            android:name="com.tqhit.battery.one.activity.animation.AnimationActivity"
            android:configChanges="orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity
            android:name="com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.NoActionBar"
            android:configChanges="orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity
            android:name="com.tqhit.battery.one.activity.password.EnterPasswordActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.NoActionBar"
            android:configChanges="orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>
            
        <activity
            android:name="com.tqhit.battery.one.features.new_discharge.presentation.TestNewDischargeActivity"
            android:exported="true"
            android:theme="@style/Theme.AppCompat"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="battery" android:host="test_discharge" />
            </intent-filter>
        </activity>
        
        <!-- Legacy TestNewChargeActivity removed - was in legacy directory -->
        
        <activity
            android:name="com.tqhit.battery.one.activity.debug.DebugActivity"
            android:exported="true"
            android:theme="@style/Theme.AppCompat"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service
            android:name="com.tqhit.battery.one.service.BatteryMonitorService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse"  />
        <service
            android:name="com.tqhit.battery.one.service.ChargingOverlayService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse"  />
        <!-- Legacy services removed - replaced by CoreBatteryStatsService and UnifiedBatteryNotificationService -->
        <!-- NewChargeMonitorService, DischargeTimerService, and BatteryStatusService were in legacy directory -->
        <service
            android:name="com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse"  />
        <service
            android:name="com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse"  />
        <service
            android:name="com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse"  />
    </application>

</manifest>