package com.tqhit.battery.one.features.stats.discharge.domain

import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Enhanced screen state time tracker with robust timer service support
 * Implements gap estimation caching and improved timer logic as per requirements
 */
class ScreenStateTimeTracker {
    companion object {
        private const val TAG = "ScreenStateTimeTracker"
    }

    // UI time state flows
    private val _screenOnTimeUI = MutableStateFlow(0L)
    val screenOnTimeUI: StateFlow<Long> = _screenOnTimeUI.asStateFlow()

    private val _screenOffTimeUI = MutableStateFlow(0L)
    val screenOffTimeUI: StateFlow<Long> = _screenOffTimeUI.asStateFlow()

    // Internal tracking
    private var lastScreenState: Boolean = true // Default to screen on
    private var lastStateChangeTime: Long = System.currentTimeMillis()
    private var initialized: Boolean = false
    private var stateChangeCount: Int = 0 // Track how many times the state has changed
    private var lastIncrementTime: Long = System.currentTimeMillis()

    // Simplified approach: track session start time for OFF time calculation
    private var sessionStartTime: Long = 0L
    private var gapEstimationApplied: Boolean = false

    /**
     * Initialize the tracker with session data
     */
    fun initialize(sessionScreenOnTimeMs: Long, sessionScreenOffTimeMs: Long, isScreenOn: Boolean) {
        Log.i(TAG, "Initializing screen time tracker with session data: " +
              "ON=$sessionScreenOnTimeMs ms (${sessionScreenOnTimeMs/1000}s), " +
              "OFF=$sessionScreenOffTimeMs ms (${sessionScreenOffTimeMs/1000}s), " +
              "Current=${if(isScreenOn) "ON" else "OFF"} at ${System.currentTimeMillis()}")

        // Ensure we don't go backwards in time
        val currentOnTime = _screenOnTimeUI.value
        val currentOffTime = _screenOffTimeUI.value

        val newOnTime = kotlin.math.max(sessionScreenOnTimeMs, currentOnTime)
        val newOffTime = kotlin.math.max(sessionScreenOffTimeMs, currentOffTime)

        if (newOnTime != sessionScreenOnTimeMs || newOffTime != sessionScreenOffTimeMs) {
            Log.w(TAG, "Adjusted initialization times to prevent going backwards - " +
                  "Requested ON: ${sessionScreenOnTimeMs/1000}s → Using: ${newOnTime/1000}s, " +
                  "Requested OFF: ${sessionScreenOffTimeMs/1000}s → Using: ${newOffTime/1000}s")
        }

        _screenOnTimeUI.value = newOnTime
        _screenOffTimeUI.value = newOffTime

        lastScreenState = isScreenOn
        lastStateChangeTime = System.currentTimeMillis()
        lastIncrementTime = System.currentTimeMillis()
        initialized = true
        stateChangeCount = 0

        Log.i(TAG, "Screen time tracker initialized with lastScreenState=${if(lastScreenState) "ON" else "OFF"}, " +
              "Final times - ON: ${newOnTime/1000}s, OFF: ${newOffTime/1000}s")
    }

    /**
     * Apply gap estimation results with improved fallback for short sessions
     * Sets the screen ON time from gap estimation, OFF time will be calculated as total - ON
     */
    fun applyGapEstimationResults(screenOnTimeUI: Long, sessionStartTimeEpochMillis: Long) {
        val currentTime = System.currentTimeMillis()
        val totalSessionTime = currentTime - sessionStartTimeEpochMillis

        // IMPROVED: Add fallback mechanisms for short sessions and edge cases
        var adjustedScreenOnTime = screenOnTimeUI

        // Fallback 1: If screen ON time is 0 and session is longer than 30 seconds, use minimum reasonable value
        if (adjustedScreenOnTime == 0L && totalSessionTime > 30000L) {
            // Use 10% of session time as minimum screen ON time for sessions > 30 seconds
            adjustedScreenOnTime = (totalSessionTime * 0.1).toLong()
            Log.w(TAG, "GAP_ESTIMATION: Screen ON time was 0 for session > 30s. Using fallback: ${adjustedScreenOnTime/1000}s (10% of total)")
        }

        // Fallback 2: If screen ON time exceeds total session time, cap it
        if (adjustedScreenOnTime > totalSessionTime) {
            adjustedScreenOnTime = (totalSessionTime * 0.8).toLong() // Cap at 80% of total
            Log.w(TAG, "GAP_ESTIMATION: Screen ON time exceeded total session time. Capped to: ${adjustedScreenOnTime/1000}s (80% of total)")
        }

        // Fallback 3: For very short sessions (< 30 seconds), use proportional defaults
        if (totalSessionTime < 30000L) {
            adjustedScreenOnTime = (totalSessionTime * 0.5).toLong() // 50% for very short sessions
            Log.w(TAG, "GAP_ESTIMATION: Very short session (${totalSessionTime/1000}s). Using proportional default: ${adjustedScreenOnTime/1000}s (50%)")
        }

        // Set the adjusted screen ON time from gap estimation
        _screenOnTimeUI.value = adjustedScreenOnTime

        // Store session start time for OFF time calculation
        sessionStartTime = sessionStartTimeEpochMillis
        gapEstimationApplied = true

        // Calculate initial OFF time to verify consistency
        val calculatedOffTime = totalSessionTime - adjustedScreenOnTime
        _screenOffTimeUI.value = kotlin.math.max(0L, calculatedOffTime)

        Log.i(TAG, "GAP_ESTIMATION: Applied gap estimation with fallbacks - " +
              "Screen ON: ${adjustedScreenOnTime/1000}s, Screen OFF: ${calculatedOffTime/1000}s, " +
              "Total: ${totalSessionTime/1000}s, Session start: $sessionStartTimeEpochMillis")
        Log.i(TAG, "GAP_ESTIMATION: Screen OFF time will be dynamically calculated as (total_session_time - screen_on_time)")
    }

    /**
     * Handle screen state change with proper cache timestamp updates
     */
    fun handleScreenStateChange(isScreenOn: Boolean) {
        val now = System.currentTimeMillis()

        if (!initialized) {
            // Initialize if not done yet
            lastScreenState = isScreenOn
            lastStateChangeTime = now
            lastIncrementTime = now
            initialized = true
            stateChangeCount = 0
            Log.i(TAG, "First initialization on screen state change: ${if(isScreenOn) "ON" else "OFF"} at $now")
            return
        }

        if (isScreenOn != lastScreenState) {
            // Calculate time spent in previous state
            val timeInPreviousState = now - lastStateChangeTime

            // Update the appropriate counter
            if (lastScreenState) {
                // Previous state was ON
                val newScreenOnTime = _screenOnTimeUI.value + timeInPreviousState
                _screenOnTimeUI.value = newScreenOnTime
                Log.i(TAG, "Screen turned OFF, adding $timeInPreviousState ms (${timeInPreviousState/1000}s) to ON time, new total: $newScreenOnTime ms (${newScreenOnTime/1000}s)")
            } else {
                // Previous state was OFF
                val newScreenOffTime = _screenOffTimeUI.value + timeInPreviousState
                _screenOffTimeUI.value = newScreenOffTime
                Log.i(TAG, "Screen turned ON, adding $timeInPreviousState ms (${timeInPreviousState/1000}s) to OFF time, new total: $newScreenOffTime ms (${newScreenOffTime/1000}s)")
            }

            // No complex caching needed - OFF time is calculated from total - ON time

            // Update tracking variables
            lastScreenState = isScreenOn
            lastStateChangeTime = now
            lastIncrementTime = now  // Reset increment time when screen state changes
            stateChangeCount++
            Log.i(TAG, "Updated lastScreenState to ${if(lastScreenState) "ON" else "OFF"} (change #$stateChangeCount)")
        } else {
            // State is the same, but we might need to update the timestamp if it's been a while
            // This helps in cases where we might have missed a state change event
            val timeSinceLastChange = now - lastStateChangeTime
            if (timeSinceLastChange > 60000) { // More than 1 minute
                Log.d(TAG, "No state change, but it's been ${timeSinceLastChange/1000} seconds since last update. " +
                      "Resetting timestamp for ${if(isScreenOn) "ON" else "OFF"} state.")
                lastStateChangeTime = now
                lastIncrementTime = now  // Also reset increment time
            }
        }
    }

    /**
     * SIMPLIFIED increment method - much more reliable
     * Screen OFF time = Total session time - Screen ON time
     */
    fun incrementCurrentState(incrementMs: Long = 1000): Pair<Long, Long> {
        if (!initialized) {
            Log.i(TAG, "UI_TIMER: Not initialized yet, skipping increment at ${System.currentTimeMillis()}")
            lastIncrementTime = System.currentTimeMillis()
            return Pair(_screenOnTimeUI.value, _screenOffTimeUI.value)
        }

        val now = System.currentTimeMillis()

        // Use simplified logic if gap estimation has been applied
        if (gapEstimationApplied && sessionStartTime > 0) {
            return calculateTimesSimplified(now)
        }

        // Fallback to original logic if gap estimation not applied
        return incrementWithOriginalLogic(now)
    }

    /**
     * FIXED: Only update the time for the currently active state
     * Screen OFF time = Total session time - Screen ON time (but only when needed)
     */
    private fun calculateTimesSimplified(currentTime: Long): Pair<Long, Long> {
        // Calculate total session time
        val totalSessionTime = currentTime - sessionStartTime

        var updatedOnTime = _screenOnTimeUI.value
        var updatedOffTime = _screenOffTimeUI.value

        if (lastScreenState) {
            // Screen is ON - only update ON time, calculate OFF time from total
            // Continue tracking ON time normally (increment from last update)
            val actualElapsedMs = currentTime - lastIncrementTime
            if (actualElapsedMs > 0) {
                updatedOnTime = _screenOnTimeUI.value + actualElapsedMs
                _screenOnTimeUI.value = updatedOnTime
            }

            // Calculate OFF time as total - ON time
            val calculatedOffTime = totalSessionTime - updatedOnTime
            updatedOffTime = kotlin.math.max(0L, calculatedOffTime)
            _screenOffTimeUI.value = updatedOffTime

            Log.v(TAG, "SIMPLIFIED_TIMER: Screen ON - incremented ON time by ${actualElapsedMs}ms, calculated OFF time from total ${totalSessionTime/1000}s")
        } else {
            // Screen is OFF - use enhanced simplified calculation with gap validation
            // OFF time = total session time - current ON time (with 60-second tolerance validation)
            val calculatedOffTime = totalSessionTime - updatedOnTime
            updatedOffTime = kotlin.math.max(0L, calculatedOffTime)

            // ENHANCED: Validate the gap and apply correction if needed
            val currentTotalTime = updatedOnTime + updatedOffTime
            val gap = kotlin.math.abs(currentTotalTime - totalSessionTime)
            val maxAcceptableGap = 60000L // 60 seconds tolerance

            if (gap > maxAcceptableGap) {
                Log.w(TAG, "SIMPLIFIED_OFF_GAP: Gap detected during Screen OFF calculation! " +
                      "Gap: ${gap/1000}s > ${maxAcceptableGap/1000}s. " +
                      "Applying correction: OFF time = ${calculatedOffTime/1000}s")

                // Force the corrected OFF time to ensure total matches session duration
                updatedOffTime = calculatedOffTime
                _screenOffTimeUI.value = updatedOffTime

                Log.i(TAG, "SIMPLIFIED_OFF_GAP: Applied gap correction. " +
                      "Final times: ON=${updatedOnTime/1000}s, OFF=${updatedOffTime/1000}s, Total=${totalSessionTime/1000}s")
            } else {
                // Normal case - just update OFF time
                _screenOffTimeUI.value = updatedOffTime

                Log.v(TAG, "SIMPLIFIED_TIMER: Screen OFF - ON time stable, calculated OFF time from total. Gap=${gap/1000}s")
            }
        }

        // Update last increment time
        lastIncrementTime = currentTime

        // Log every 10 seconds to avoid noise
        if ((updatedOnTime / 1000) % 10 == 0L || (updatedOffTime / 1000) % 10 == 0L) {
            Log.d(TAG, "SIMPLIFIED_TIMER: Updated times - " +
                  "ON: ${updatedOnTime/1000}s, OFF: ${updatedOffTime/1000}s, " +
                  "Total: ${totalSessionTime/1000}s, State: ${if(lastScreenState) "ON" else "OFF"}")
        }

        return Pair(updatedOnTime, updatedOffTime)
    }

    /**
     * Original increment logic (fallback)
     */
    private fun incrementWithOriginalLogic(now: Long): Pair<Long, Long> {
        val actualElapsedMs = now - lastIncrementTime
        lastIncrementTime = now

        if (actualElapsedMs > 2000) {
            Log.i(TAG, "UI_TIMER: Long gap between increments: ${actualElapsedMs}ms (${actualElapsedMs/1000}s)")
        }

        if (lastScreenState) {
            val newTime = _screenOnTimeUI.value + actualElapsedMs
            _screenOnTimeUI.value = newTime
            Log.d(TAG, "UI_TIMER: Incremented ON time by ${actualElapsedMs}ms to ${newTime}ms (${newTime/1000}s)")
            return Pair(newTime, _screenOffTimeUI.value)
        } else {
            val newTime = _screenOffTimeUI.value + actualElapsedMs
            _screenOffTimeUI.value = newTime
            Log.d(TAG, "UI_TIMER: Incremented OFF time by ${actualElapsedMs}ms to ${newTime}ms (${newTime/1000}s)")
            return Pair(_screenOnTimeUI.value, newTime)
        }
    }

    /**
     * Reset tracking data
     */
    fun reset() {
        _screenOnTimeUI.value = 0L
        _screenOffTimeUI.value = 0L
        lastScreenState = true
        lastStateChangeTime = System.currentTimeMillis()
        lastIncrementTime = System.currentTimeMillis()
        initialized = false
        stateChangeCount = 0

        // Reset simplified tracking
        sessionStartTime = 0L
        gapEstimationApplied = false

        Log.i(TAG, "Screen time tracker reset at ${System.currentTimeMillis()}")
    }

    /**
     * Get the current tracked times
     */
    fun getCurrentTimes(): Pair<Long, Long> {
        return Pair(_screenOnTimeUI.value, _screenOffTimeUI.value)
    }

    /**
     * Force set the current screen state without updating times
     * Use this for correcting state mismatches only
     * FIXED: Proper cache timestamp management
     */
    fun forceSetScreenState(isScreenOn: Boolean) {
        if (lastScreenState != isScreenOn) {
            Log.i(TAG, "Force setting screen state from ${if(lastScreenState) "ON" else "OFF"} to ${if(isScreenOn) "ON" else "OFF"} at ${System.currentTimeMillis()}")

            val currentTime = System.currentTimeMillis()

            // No complex cache management needed with simplified approach
            Log.d(TAG, "SIMPLIFIED: Force set screen state to ${if(isScreenOn) "ON" else "OFF"}")

            // Update state tracking variables
            lastScreenState = isScreenOn
            lastStateChangeTime = currentTime
            lastIncrementTime = currentTime
            stateChangeCount++
        }
    }

    /**
     * Force set the screen times to specific values
     * Use this for correcting time drift or enforcing constraints
     */
    fun forceSetTimes(onTimeMs: Long, offTimeMs: Long) {
        Log.i(TAG, "Force setting times - ON: ${onTimeMs/1000}s, OFF: ${offTimeMs/1000}s " +
              "(Previous: ON: ${_screenOnTimeUI.value/1000}s, OFF: ${_screenOffTimeUI.value/1000}s)")

        _screenOnTimeUI.value = onTimeMs
        _screenOffTimeUI.value = offTimeMs

        // No cache management needed with simplified approach

        // Reset increment time to prevent immediate drift
        lastIncrementTime = System.currentTimeMillis()
    }

    /**
     * Force set the screen OFF time for gap correction during Screen OFF periods
     * This implements the simplified calculation method as per requirements
     */
    fun forceSetScreenOffTime(correctedOffTime: Long) {
        Log.i(TAG, "FORCE_SET_OFF_TIME: Setting Screen OFF time to ${correctedOffTime/1000}s for gap correction")
        _screenOffTimeUI.value = correctedOffTime

        // Update the last increment time to prevent immediate overwrite
        lastIncrementTime = System.currentTimeMillis()

        Log.i(TAG, "FORCE_SET_OFF_TIME: Screen OFF time corrected. " +
              "New times - ON: ${_screenOnTimeUI.value/1000}s, OFF: ${correctedOffTime/1000}s")
    }

    /**
     * Get debug information about the current state
     */
    fun getDebugInfo(): String {
        val totalSessionTime = if (sessionStartTime > 0) {
            (System.currentTimeMillis() - sessionStartTime) / 1000
        } else {
            0L
        }

        return "ScreenStateTimeTracker Debug Info (SIMPLIFIED):\n" +
               "- Initialized: $initialized\n" +
               "- Current State: ${if(lastScreenState) "ON" else "OFF"}\n" +
               "- State Changes: $stateChangeCount\n" +
               "- Current Times: ON=${_screenOnTimeUI.value/1000}s, OFF=${_screenOffTimeUI.value/1000}s\n" +
               "- Gap Estimation Applied: $gapEstimationApplied\n" +
               "- Session Start Time: $sessionStartTime\n" +
               "- Total Session Time: ${totalSessionTime}s\n" +
               "- Last State Change: $lastStateChangeTime\n" +
               "- Last Increment: $lastIncrementTime"
    }
}
